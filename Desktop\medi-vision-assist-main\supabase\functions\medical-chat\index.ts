
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ChatRequest {
  question: string
  medicineName?: string
  userId?: string
}

const OPENROUTER_API_KEY = Deno.env.get('OPENROUTER_API_KEY')

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  console.log(`🚀 Medical chat function called - Method: ${req.method}`)

  try {
    // Step 1: Validate environment variables
    console.log('🔍 Step 1: Validating environment variables...')

    if (!OPENROUTER_API_KEY) {
      console.error('❌ OPENROUTER_API_KEY environment variable is not set')
      throw new Error('OpenRouter API key is not configured')
    }
    console.log('✅ OpenRouter API key is set')

    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    if (!supabaseUrl) {
      console.error('❌ SUPABASE_URL environment variable is not set')
      throw new Error('Supabase URL is not configured')
    }

    if (!supabaseServiceKey) {
      console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is not set')
      throw new Error('Supabase service role key is not configured')
    }

    console.log('✅ Supabase environment variables are set')

    // Step 2: Parse request body
    console.log('🔍 Step 2: Parsing request body...')
    let requestBody
    try {
      requestBody = await req.json()
    } catch (parseError) {
      console.error('❌ Failed to parse request body:', parseError)
      throw new Error('Invalid JSON in request body')
    }

    const { question, medicineName, userId }: ChatRequest = requestBody

    console.log('📥 Request payload:', {
      question: question?.substring(0, 100) + (question?.length > 100 ? '...' : ''),
      medicineName,
      userId,
      hasQuestion: !!question
    })

    if (!question || question.trim() === '') {
      console.error('❌ Question is missing or empty')
      throw new Error('Question is required and cannot be empty')
    }

    console.log(`🤖 Processing medical chat request for user: ${userId || 'anonymous'}`)
    
    const startTime = Date.now()
    
    // Create the prompt for GPT
    const systemPrompt = `You are a knowledgeable medical assistant with extensive training on over 100 medical topics. You provide accurate, helpful information about medicines, supplements, health conditions, and general medical guidance based on established medical knowledge.

IMPORTANT GUIDELINES:
- Always base your answers on established medical knowledge and evidence-based medicine
- Never provide specific medical advice - always recommend consulting healthcare providers for personalized medical decisions
- Be clear about limitations and when professional consultation is needed
- Focus on general safety information, drug interactions, side effects, and well-known medical facts
- If unsure about something, clearly state the limitation and suggest consulting a healthcare professional
- You can answer questions about general health, nutrition, exercise, preventive care, and wellness
- For medication questions, provide general information about common uses, dosages, and precautions
- Always emphasize the importance of professional medical consultation for specific medical decisions

${medicineName && medicineName !== 'General Medical Assistant' ? `Context: User is asking about ${medicineName}` : ''}

User question: ${question}

Provide a helpful, accurate response while emphasizing the importance of professional medical consultation for specific medical decisions.`

    // Step 3: Make request to OpenRouter API
    console.log('🔍 Step 3: Making request to OpenRouter API...')

    let response
    try {
      response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://ygkxdctaraeragizxfbt.supabase.co',
          'X-Title': 'MediVision Assist'
        },
        body: JSON.stringify({
          model: 'openai/gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: systemPrompt
            },
            {
              role: 'user',
              content: question
            }
          ],
          max_tokens: 500,
          temperature: 0.3
        })
      })
    } catch (fetchError) {
      console.error('❌ Network error calling OpenRouter API:', fetchError)
      throw new Error('Network error: Unable to connect to AI service')
    }

    console.log(`📡 OpenRouter API response status: ${response.status}`)

    if (!response.ok) {
      let errorText = 'Unknown error'
      try {
        errorText = await response.text()
      } catch (e) {
        console.error('Failed to read error response:', e)
      }

      console.error('❌ OpenRouter API error:', {
        status: response.status,
        statusText: response.statusText,
        errorText
      })

      // Provide specific error messages based on status code
      if (response.status === 401) {
        throw new Error('OpenRouter API authentication failed - check API key')
      } else if (response.status === 429) {
        throw new Error('OpenRouter API rate limit exceeded - please try again later')
      } else if (response.status >= 500) {
        throw new Error('OpenRouter API server error - please try again later')
      } else {
        throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`)
      }
    }

    let chatResponse
    try {
      chatResponse = await response.json()
    } catch (jsonError) {
      console.error('❌ Failed to parse OpenRouter API response:', jsonError)
      throw new Error('Invalid response format from AI service')
    }

    console.log('📥 OpenRouter API response structure:', {
      hasChoices: !!chatResponse.choices,
      choicesLength: chatResponse.choices?.length,
      hasFirstChoice: !!chatResponse.choices?.[0],
      hasMessage: !!chatResponse.choices?.[0]?.message,
      hasContent: !!chatResponse.choices?.[0]?.message?.content
    })

    const reply = chatResponse.choices?.[0]?.message?.content

    if (!reply || reply.trim() === '') {
      console.error('❌ Empty or missing reply from AI model:', chatResponse)
      throw new Error('AI service returned empty response')
    }

    const responseTime = Date.now() - startTime
    console.log(`✅ Chat response generated in ${responseTime}ms`)

    // Step 4: Store the chat session if userId provided
    let sessionId = null
    if (userId && supabaseUrl && supabaseServiceKey) {
      console.log('🔍 Step 4: Storing chat session in database...')

      try {
        const supabase = createClient(supabaseUrl, supabaseServiceKey)

        const { data: session, error } = await supabase
          .from('chat_sessions')
          .insert({
            user_id: userId,
            medicine_name: medicineName || 'General Medical Query',
            question: question,
            response: reply,
            response_time_ms: responseTime
          })
          .select('id')
          .single()

        if (error) {
          console.error('❌ Failed to store chat session:', {
            error: error.message,
            code: error.code,
            details: error.details,
            hint: error.hint
          })
          // Don't throw here - we still want to return the chat response
        } else {
          sessionId = session?.id
          console.log(`✅ Chat session stored with ID: ${sessionId}`)
        }
      } catch (dbError) {
        console.error('❌ Database error storing chat session:', dbError)
        // Don't throw here - we still want to return the chat response
      }
    } else {
      console.log('ℹ️ Skipping database storage (missing userId or Supabase config)')
    }

    return new Response(
      JSON.stringify({
        success: true,
        reply,
        sessionId,
        responseTime
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('❌ Medical chat error:', error)
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    })

    // Determine if this is a configuration error or runtime error
    const isConfigError = error.message?.includes('not configured') ||
                         error.message?.includes('environment variable')

    const errorMessage = isConfigError
      ? 'Service configuration error. Please contact support.'
      : 'Failed to process chat request'

    return new Response(
      JSON.stringify({
        success: false,
        error: errorMessage,
        reply: "I apologize, but I'm having trouble processing your request right now. Please try again later or consult with a healthcare professional for medical questions.",
        debug: Deno.env.get('NODE_ENV') === 'development' ? error.message : undefined
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})
